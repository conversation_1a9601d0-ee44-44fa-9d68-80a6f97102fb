# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# AdonisJS build output
build/
dist/

# Environment variables
.env
.env.*
!.env.example

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database files (if using SQLite)
*.sqlite
*.sqlite3

# Uploads directory (if your app handles file uploads)
public/uploads/
tmp/uploads/

# AdonisJS specific
.adonisrc.json
ace

# TypeScript
*.tsbuildinfo

# Docker - Keep these files committed:
# !docker-compose.yml
# !docker-compose.override.yml
# !Dockerfile

# Docker ignore patterns
docker-compose.*.yml
docker-compose.*.yaml
.dockerignore

# Docker data volumes and development files
data/
.docker/
*.dockerignore

# Docker build artifacts (if any)
.docker-build/