version: "3.9"

services:
  app:
    build:
      context: .
      target: development
    container_name: adonis_app
    volumes:
      - .:/app          # mount project source
      - /app/node_modules # avoid overwriting node_modules
    ports:
      - "3333:3333"
    environment:
      NODE_ENV: development
      DB_CONNECTION: pg
      PG_HOST: postgres
      PG_PORT: 5432
      PG_USER: adonis
      PG_PASSWORD: secret
      PG_DB_NAME: adonis_db
    depends_on:
      - postgres

  postgres:
    image: postgres:15
    container_name: adonis_postgres
    restart: always
    environment:
      POSTGRES_USER: adonis
      POSTGRES_PASSWORD: secret
      POSTGRES_DB: adonis_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  adminer:
    image: adminer
    container_name: adonis_adminer
    restart: always
    ports:
      - "8080:8080"

volumes:
  postgres_data:
